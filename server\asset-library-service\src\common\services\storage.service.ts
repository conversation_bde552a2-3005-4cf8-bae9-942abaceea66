import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { LoggerService } from './logger.service';
import * as crypto from 'crypto';
import * as path from 'path';

export interface UploadResult {
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  etag: string;
  url: string;
}

export interface UploadOptions {
  bucket?: string;
  prefix?: string;
  generateThumbnail?: boolean;
  allowedMimeTypes?: string[];
  maxFileSize?: number;
}

@Injectable()
export class StorageService implements OnModuleInit {
  private minioClient: Minio.Client;
  private defaultBucket: string;
  private isConnected = false;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {
    this.defaultBucket = this.configService.get('MINIO_BUCKET', 'assets');
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      this.minioClient = new Minio.Client({
        endPoint: this.configService.get('MINIO_ENDPOINT', 'localhost'),
        port: this.configService.get('MINIO_PORT', 9000),
        useSSL: this.configService.get('MINIO_USE_SSL', false),
        accessKey: this.configService.get('MINIO_ACCESS_KEY', 'minioadmin'),
        secretKey: this.configService.get('MINIO_SECRET_KEY', 'minioadmin'),
      });

      // 检查连接
      await this.minioClient.listBuckets();
      this.isConnected = true;

      // 确保默认存储桶存在
      await this.ensureBucketExists(this.defaultBucket);

      this.logger.log('MinIO存储服务初始化成功', 'StorageService');
    } catch (error) {
      this.logger.error('MinIO存储服务初始化失败', error instanceof Error ? error.message : String(error), 'StorageService');
      this.isConnected = false;
    }
  }

  /**
   * 确保存储桶存在
   */
  private async ensureBucketExists(bucketName: string): Promise<void> {
    try {
      const exists = await this.minioClient.bucketExists(bucketName);
      if (!exists) {
        await this.minioClient.makeBucket(bucketName);
        this.logger.log(`存储桶创建成功: ${bucketName}`, 'StorageService');
      }
    } catch (error) {
      this.logger.error(`存储桶检查/创建失败: ${bucketName}`, error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 上传文件
   */
  async uploadFile(
    file: Buffer | string,
    originalName: string,
    options: UploadOptions = {},
  ): Promise<UploadResult> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    const {
      bucket = this.defaultBucket,
      prefix = '',
      allowedMimeTypes,
      maxFileSize,
    } = options;

    // 生成唯一文件名
    const fileExtension = path.extname(originalName);
    const fileName = this.generateFileName(originalName, fileExtension);
    const filePath = prefix ? `${prefix}/${fileName}` : fileName;

    // 文件验证
    if (allowedMimeTypes && allowedMimeTypes.length > 0) {
      const mimeType = this.getMimeType(fileExtension);
      if (!allowedMimeTypes.includes(mimeType)) {
        throw new Error(`不支持的文件类型: ${mimeType}`);
      }
    }

    if (maxFileSize && Buffer.byteLength(file) > maxFileSize) {
      throw new Error(`文件大小超过限制: ${maxFileSize} bytes`);
    }

    try {
      // 确保存储桶存在
      await this.ensureBucketExists(bucket);

      // 上传文件
      const uploadInfo = await this.minioClient.putObject(
        bucket,
        filePath,
        file,
        Buffer.byteLength(file),
        {
          'Content-Type': this.getMimeType(fileExtension),
          'X-Amz-Meta-Original-Name': originalName,
          'X-Amz-Meta-Upload-Time': new Date().toISOString(),
        },
      );

      // 生成访问URL
      const url = await this.getFileUrl(bucket, filePath);

      const result: UploadResult = {
        fileName,
        filePath,
        fileSize: Buffer.byteLength(file),
        mimeType: this.getMimeType(fileExtension),
        etag: uploadInfo.etag,
        url,
      };

      this.logger.log(`文件上传成功: ${filePath}`, 'StorageService');
      return result;
    } catch (error) {
      this.logger.error('文件上传失败', error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(bucket: string, filePath: string): Promise<Buffer> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      const stream = await this.minioClient.getObject(bucket, filePath);
      const chunks: Buffer[] = [];

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error('文件下载失败', error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(bucket: string, filePath: string): Promise<void> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      await this.minioClient.removeObject(bucket, filePath);
      this.logger.log(`文件删除成功: ${filePath}`, 'StorageService');
    } catch (error) {
      this.logger.error('文件删除失败', error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(bucket: string, filePath: string): Promise<any> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      return await this.minioClient.statObject(bucket, filePath);
    } catch (error) {
      this.logger.error('获取文件信息失败', error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 生成预签名URL
   */
  async getPresignedUrl(
    bucket: string,
    filePath: string,
    expiry: number = 3600,
  ): Promise<string> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      return await this.minioClient.presignedGetObject(bucket, filePath, expiry);
    } catch (error) {
      this.logger.error('生成预签名URL失败', error instanceof Error ? error.message : String(error), 'StorageService');
      throw error;
    }
  }

  /**
   * 获取文件访问URL
   */
  async getFileUrl(bucket: string, filePath: string): Promise<string> {
    const endpoint = this.configService.get('MINIO_ENDPOINT', 'localhost');
    const port = this.configService.get('MINIO_PORT', 9000);
    const useSSL = this.configService.get('MINIO_USE_SSL', false);
    const protocol = useSSL ? 'https' : 'http';
    
    return `${protocol}://${endpoint}:${port}/${bucket}/${filePath}`;
  }

  /**
   * 列出文件
   */
  async listFiles(bucket: string, prefix?: string): Promise<any[]> {
    if (!this.isConnected) {
      throw new Error('存储服务未连接');
    }

    try {
      const objects: any[] = [];
      const stream = this.minioClient.listObjects(bucket, prefix, true);

      return new Promise((resolve, reject) => {
        stream.on('data', (obj) => objects.push(obj));
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error('列出文件失败', error.message, 'StorageService');
      throw error;
    }
  }

  /**
   * 生成唯一文件名
   */
  private generateFileName(originalName: string, extension: string): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    const baseName = path.basename(originalName, extension);
    const safeName = baseName.replace(/[^a-zA-Z0-9]/g, '_');
    
    return `${timestamp}_${random}_${safeName}${extension}`;
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.glb': 'model/gltf-binary',
      '.gltf': 'model/gltf+json',
      '.fbx': 'application/octet-stream',
      '.obj': 'application/octet-stream',
      '.mtl': 'text/plain',
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.7z': 'application/x-7z-compressed',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.ogg': 'audio/ogg',
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.json': 'application/json',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * 检查连接状态
   */
  isStorageConnected(): boolean {
    return this.isConnected;
  }
}
