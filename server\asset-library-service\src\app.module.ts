import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

// 配置模块
import { DatabaseConfig } from './config/database.config';
import { RedisConfig } from './config/redis.config';
import { MinioConfig } from './config/minio.config';
import { ElasticsearchConfig } from './config/elasticsearch.config';

// 核心模块
import { AssetsModule } from './modules/assets/assets.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { TagsModule } from './modules/tags/tags.module';
import { VersionsModule } from './modules/versions/versions.module';
import { SearchModule } from './modules/search/search.module';
import { UploadModule } from './modules/upload/upload.module';
import { AuthModule } from './modules/auth/auth.module';

// 公共服务
import { LoggerService } from './common/services/logger.service';
import { CacheService } from './common/services/cache.service';
import { StorageService } from './common/services/storage.service';
import { HealthController } from './common/controllers/health.controller';

// 实体
import { Asset } from './modules/assets/entities/asset.entity';
import { Category } from './modules/categories/entities/category.entity';
import { Tag } from './modules/tags/entities/tag.entity';
import { AssetVersion } from './modules/versions/entities/asset-version.entity';
import { User } from './modules/auth/entities/user.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'asset_library'),
        entities: [Asset, Category, Tag, AssetVersion, User],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),
    
    // Elasticsearch模块
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        node: configService.get('ELASTICSEARCH_NODE', 'http://localhost:9200'),
        auth: {
          username: configService.get('ELASTICSEARCH_USERNAME'),
          password: configService.get('ELASTICSEARCH_PASSWORD'),
        },
      }),
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'asset-library-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // 业务模块
    AssetsModule,
    CategoriesModule,
    TagsModule,
    VersionsModule,
    SearchModule,
    UploadModule,
    AuthModule,
  ],
  controllers: [HealthController],
  providers: [
    LoggerService,
    CacheService,
    StorageService,
    DatabaseConfig,
    RedisConfig,
    MinioConfig,
    ElasticsearchConfig,
  ],
  exports: [
    LoggerService,
    CacheService,
    StorageService,
  ],
})
export class AppModule {}
