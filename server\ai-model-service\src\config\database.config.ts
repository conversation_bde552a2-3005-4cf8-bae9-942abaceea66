/**
 * 数据库配置
 */
import { registerAs } from '@nestjs/config';
import type {  TypeOrmModuleOptions  } from '@nestjs/typeorm';
import { AIModel } from '../entities/ai-model.entity';
import { ModelVersion } from '../entities/model-version.entity';
import { InferenceLog } from '../entities/inference-log.entity';
import { ModelMetrics } from '../entities/model-metrics.entity';

export const databaseConfig = registerAs('database', (): TypeOrmModuleOptions => ({
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'ai_model_service',
  entities: [
    AIModel,
    ModelVersion,
    InferenceLog,
    ModelMetrics,
  ],
  synchronize: process.env.NODE_ENV === 'development',
  logging: process.env.NODE_ENV === 'development',
  timezone: '+08:00',
  charset: 'utf8mb4',
  extra: {
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
  retryAttempts: 3,
  retryDelay: 3000,
}));
